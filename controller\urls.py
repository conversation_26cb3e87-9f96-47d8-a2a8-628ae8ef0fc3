from flask import Flask
from controller.config.cmd_script import Restart
from controller.config.config_controller import WriteYml, ReadYml
from controller.memory.memory_controller import MemorySearch, MemoryResetInputdata
from controller.userinfo.userinfo_controller import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ScoreRank, ScoreStats
from controller.livebroadcast.live_controller import (
    Cha<PERSON>, ChatReply, HttpRecharge, HttpCmd, HttpSay, HttpEmote, 
    HttpScene, HttpSing, HttpDraw, Msg, SongList,
    OperScoreController, ScoreRankController, UserScoreController
)

def bind_urls(app):
    """绑定所有URL路由"""
    print("绑定所有URL路由----------")
    
    # ============= 聊天相关路由 =============
    app.add_url_rule('/chat', view_func=Chat.as_view('chat'), methods=['GET', 'POST'])
    app.add_url_rule('/chatlist', view_func=ChatList.as_view('chatlist'), methods=['GET', 'POST'])
    app.add_url_rule('/chatreply', view_func=ChatReply.as_view('chatreply'), methods=['GET', 'POST'])
    app.add_url_rule('/msg', view_func=Msg.as_view('msg'), methods=['POST'])
    
    # ============= 命令相关路由 =============
    app.add_url_rule('/cmd', view_func=HttpCmd.as_view('cmd'), methods=['GET', 'POST'])
    app.add_url_rule('/say', view_func=HttpSay.as_view('say'), methods=['POST'])
    app.add_url_rule('/emote', view_func=HttpEmote.as_view('emote'), methods=['POST'])
    
    # ============= 场景和功能相关路由 =============
    app.add_url_rule('/http_scene', view_func=HttpScene.as_view('http_scene'), methods=['GET', 'POST'])
    app.add_url_rule('/http_sing', view_func=HttpSing.as_view('http_sing'), methods=['GET', 'POST'])
    app.add_url_rule('/http_draw', view_func=HttpDraw.as_view('http_draw'), methods=['GET', 'POST'])
    app.add_url_rule('/songlist', view_func=SongList.as_view('songlist'), methods=['GET'])
    
    # ============= 积分系统相关路由 =============
    # 积分充值
    app.add_url_rule('/recharge', view_func=HttpRecharge.as_view('recharge'), methods=['POST'])
    
    # 积分操作
    app.add_url_rule('/score/operation', view_func=OperScoreController.as_view('score_operation'), methods=['POST'])
    
    # 积分查询
    app.add_url_rule('/score/user', view_func=UserScoreController.as_view('user_score'), methods=['GET'])
    app.add_url_rule('/score/rank', view_func=ScoreRankController.as_view('score_rank_api'), methods=['GET'])
    
    # 积分记录和统计
    app.add_url_rule('/scorerecord', view_func=ScoreRecord.as_view('scorerecord'), methods=['GET'])
    app.add_url_rule('/score/stats', view_func=ScoreStats.as_view('score_stats'), methods=['GET'])
    
    # 管理端积分排行榜
    app.add_url_rule('/score/rank/manage', view_func=ScoreRank.as_view('score_rank_manage'), methods=['GET'])
    
    # ============= 配置相关路由 =============
    app.add_url_rule('/oper/write_yml', view_func=WriteYml.as_view('write_yml'), methods=['GET', 'POST'])
    app.add_url_rule('/oper/read_yml', view_func=ReadYml.as_view('read_yml'), methods=['GET', 'POST'])
    
    # ============= 内存相关路由 =============
    app.add_url_rule('/memory_search', view_func=MemorySearch.as_view('memory_search'), methods=['GET', 'POST'])
    app.add_url_rule('/memory_reset_inputdata', view_func=MemoryResetInputdata.as_view('memory_reset_inputdata'), methods=['GET', 'POST'])
    
    # ============= 用户管理相关路由 =============
    app.add_url_rule('/userlist', view_func=UserList.as_view('userlist'), methods=['GET', 'POST'])
    
    # ============= 系统管理路由 =============
    app.add_url_rule('/restart', view_func=Restart.as_view('restart'), methods=['GET', 'POST'])
    
    print("URL路由绑定完成----------")