from flask import jsonify, request
import json
import uuid
import threading
from typing import Dict, Any, Optional
from controller.baseview import BaseView
from func.log.default_log import DefaultLog
from func.gobal.data import VtuberData

from func.llm.llm_core import LLmCore
from func.cmd.cmd_core import CmdCore
from func.tts.tts_core import TTsCore
from func.draw.draw_core import DrawCore
from func.sing.sing_core import SingCore
from func.dance.dance_core import DanceCore
from func.vtuber.action_oper import ActionOper
from func.vtuber.emote_oper import EmoteOper
from func.memory.memory_core import MemoryCore
from func.image.image_core import ImageCore
from func.search.search_core import SearchCore
from func.score.oper_score import OperScore

from func.entrance.entrance_core import EntranceCore
# from func.entrance.entrance_core import msg_deal

class HttpCmd(BaseView):
    def get(self):
        try:
            cmdstr = request.args.get('cmd')
            traceid = request.args.get('traceid')
            query = request.args.get('query')
            uid = request.args.get('uid')
            user_name = request.args.get('user_name')
            DefaultLog.getLogger().info(f"执行指令：{cmdstr}")
            result = CmdCore().cmd(traceid, query, uid, user_name)
            return jsonify(result)
        except Exception as e:
            print(e)
            return jsonify({"status": "失败", "content": str(e)})

class HttpSay(BaseView):
    def post(self):
        try:
            print("HttpSay-----------")
            # data = request.get_json()
            # 来自网页则是'Content-Type': 'text/plain'，不能用get_json()
            # print(request.args)
            data = request.get_data()
            text = data.decode('utf-8')
            print(data)
            result = TTsCore().tts_say(text)
            return jsonify(result)
        except Exception as e:
            print(e)
            return jsonify({"status": "失败", "content": str(e)})

class HttpDraw(BaseView):
    def get(self):
        try:
            drawname = request.args.get('drawname')
            drawcontent = request.args.get('content')
            username = request.args.get('username')
            result = DrawCore().http_draw(drawname, drawcontent, username)
            return jsonify(result)
        except Exception as e:
            print(e)
            return jsonify({"status": "失败", "content": str(e)})

class HttpSing(BaseView):
    def get(self):
        try:
            songname = request.args.get('songname')
            username = request.args.get('username')
            result = SingCore().http_sing(songname, username)

            # openId = request.args.get('openId')
            # result = SingCore().http_sing(songname, openId)
            return jsonify(result)
        except Exception as e:
            print(e)
            return jsonify({"status": "失败", "content": str(e)})

class HttpEmote(BaseView):
    def post(self):
        try:
            data = request.get_json()
            emote_ws = EmoteOper()
            emote_thread1 = threading.Thread(target=emote_ws.emoteOper, args=(data,))
            emote_thread1.start()
            return jsonify({"status": "成功"})
        except Exception as e:
            return jsonify({"status": "失败", "content": str(e)})

class HttpScene(BaseView):
    def get(self):
        try:
            scenename = request.args.get('scenename')
            VtuberData().changeScene(scenename)
            return jsonify({"status": "成功"})
        except Exception as e:
            print(e)
            return jsonify({"status": "失败", "content": str(e)})

class ChatReply(BaseView):
    def get(self):
        try:
            chatId = request.args.get('chatId')
            result = LLmCore().http_chatreply(chatId)
            return jsonify(result)
        except Exception as e:
            print(e)
            return jsonify({"status": "失败", "content": str(e)})

class Chat(BaseView):
    def get(self):
        try:
            query = request.args.get('query')
            username = request.args.get('username', '')
            result = EntranceCore().msg_deal(query, username)
            return jsonify(result)
        except Exception as e:
            print("Chat异常------", e)
            return jsonify({"status": "失败", "content": str(e)})
        
class Msg(BaseView):
    def post(self):
        try:
            print("request.Msg--------------------")
            # query = request.args.get('query')
            # username = request.args.get('username', '')
            
            data = request.json
            query = data["msg"]  # 获取弹幕内容
            uid = data["uid"]  # 获取用户昵称
            user_name = data["username"]  # 获取用户昵称
            uface = data["uface"]  # 获取用户头像?
            # openId = data["openId"]  # 获取用户openid
            traceid = str(uuid.uuid4())
            channel = data.get("channel", "api")

            result = EntranceCore().msg_deal(traceid, channel, query, uid, user_name, uface)

            # OperScore().oper_score("kekeid", user_name, 1, uface, "聊天")

            return jsonify(result)
        except Exception as e:
            print("request.Msg异常------", e)
            print(jsonify({"status": "失败", "content": str(e)}))
            
            return jsonify({"status": "失败", "content": str(e)})

class HttpRecharge(BaseView):
    """积分充值控制器"""
    
    def __init__(self):
        self.operScore = OperScore()
        self.log = DefaultLog().getLogger()
    
    def post(self) -> Dict[str, Any]:
        """
        积分充值接口
        :return: 充值结果
        """
        try:
            # 获取请求数据
            data = request.get_json()
            if not data:
                return self._build_error_response("请求数据为空")
            
            # 参数验证
            validation_result = self._validate_recharge_params(data)
            if not validation_result['valid']:
                return self._build_error_response(validation_result['error'])
            
            score = validation_result['score']
            openid = validation_result['openid']
            oper = validation_result.get('oper', 'recharge')
            
            self.log.info(f"开始积分充值: openid={openid}, score={score}, oper={oper}")
            
            # 执行充值操作
            result = self.operScore.recharge_score(openid, score, oper)
            
            if result is not None:
                self.log.info(f"积分充值成功: openid={openid}, 当前积分={result}")
                return self._build_success_response(result, openid, score, oper)
            else:
                self.log.error(f"积分充值失败: openid={openid}")
                return self._build_error_response("充值失败，请检查用户信息或积分数量")
                
        except Exception as e:
            self.log.error(f"积分充值异常: {str(e)}")
            return self._build_error_response(f"系统异常: {str(e)}")
    
    def _validate_recharge_params(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证充值参数"""
        try:
            # 检查必需参数
            if 'openid' not in data:
                return {'valid': False, 'error': '缺少openid参数'}
            if 'score' not in data:
                return {'valid': False, 'error': '缺少score参数'}
            
            openid = str(data['openid']).strip()
            if not openid:
                return {'valid': False, 'error': 'openid不能为空'}
            
            # 验证积分数量
            try:
                score = int(data['score'])
            except (ValueError, TypeError):
                return {'valid': False, 'error': 'score必须是数字'}
            
            if score == 0:
                return {'valid': False, 'error': '积分数量不能为0'}
            
            if abs(score) > 10000:  # 限制单次操作的积分数量
                return {'valid': False, 'error': '单次操作积分数量不能超过10000'}
            
            # 获取操作类型
            oper = data.get('oper', 'recharge' if score > 0 else 'deduct')
            
            return {
                'valid': True,
                'openid': openid,
                'score': score,
                'oper': oper
            }
            
        except Exception as e:
            return {'valid': False, 'error': f'参数验证异常: {str(e)}'}
    
    def _build_success_response(self, new_score: int, openid: str, score_change: int, oper: str) -> Dict[str, Any]:
        """构建成功响应"""
        return jsonify({
            'status': 'success',
            'message': '积分操作成功',
            'data': {
                'openid': openid,
                'score_change': score_change,
                'current_score': new_score,
                'operation': oper
            }
        })
    
    def _build_error_response(self, error_msg: str) -> Dict[str, Any]:
        """构建错误响应"""
        return jsonify({
            'status': 'error',
            'message': error_msg,
            'data': None
        }), 400

class OperScoreController(BaseView):
    """积分操作控制器"""
    
    def __init__(self):
        self.operScore = OperScore()
        self.log = DefaultLog().getLogger()
    
    def post(self) -> Dict[str, Any]:
        """
        通用积分操作接口
        :return: 操作结果
        """
        try:
            data = request.get_json()
            if not data:
                return self._build_error_response("请求数据为空")
            
            # 参数验证
            validation_result = self._validate_operation_params(data)
            if not validation_result['valid']:
                return self._build_error_response(validation_result['error'])
            
            # 执行积分操作
            result = self.operScore.oper_score(
                openId=validation_result['openId'],
                user_name=validation_result['user_name'],
                score=validation_result['score'],
                uface=validation_result['uface'],
                oper=validation_result['oper']
            )
            
            if result is not None:
                return self._build_success_response(validation_result, result)
            else:
                return self._build_error_response("积分操作失败")
                
        except Exception as e:
            self.log.error(f"积分操作异常: {str(e)}")
            return self._build_error_response(f"系统异常: {str(e)}")
    
    def _validate_operation_params(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证操作参数"""
        required_fields = ['openId', 'user_name', 'score', 'oper']
        
        for field in required_fields:
            if field not in data:
                return {'valid': False, 'error': f'缺少{field}参数'}
        
        try:
            openId = str(data['openId']).strip()
            user_name = str(data['user_name']).strip()
            uface = data.get('uface', '')
            oper = str(data['oper']).strip()
            
            if not openId:
                return {'valid': False, 'error': 'openId不能为空'}
            if not user_name:
                return {'valid': False, 'error': 'user_name不能为空'}
            if not oper:
                return {'valid': False, 'error': 'oper不能为空'}
            
            try:
                score = int(data['score'])
            except (ValueError, TypeError):
                return {'valid': False, 'error': 'score必须是数字'}
            
            return {
                'valid': True,
                'openId': openId,
                'user_name': user_name,
                'score': score,
                'uface': uface,
                'oper': oper
            }
            
        except Exception as e:
            return {'valid': False, 'error': f'参数验证异常: {str(e)}'}
    
    def _build_success_response(self, params: Dict[str, Any], current_score: int) -> Dict[str, Any]:
        """构建成功响应"""
        return jsonify({
            'status': 'success',
            'message': '积分操作成功',
            'data': {
                'openId': params['openId'],
                'user_name': params['user_name'],
                'score_change': params['score'],
                'current_score': current_score,
                'operation': params['oper']
            }
        })
    
    def _build_error_response(self, error_msg: str) -> Dict[str, Any]:
        """构建错误响应"""
        return jsonify({
            'status': 'error',
            'message': error_msg,
            'data': None
        }), 400

class ScoreRankController(BaseView):
    """积分排行榜控制器"""
    
    def __init__(self):
        self.operScore = OperScore()
        self.log = DefaultLog().getLogger()
    
    def get(self) -> Dict[str, Any]:
        """
        获取积分排行榜
        :return: 排行榜数据
        """
        try:
            limit = request.args.get('limit', 10, type=int)
            limit = max(1, min(100, limit))  # 限制在1-100之间
            
            self.log.info(f"查询积分排行榜: limit={limit}")
            
            rank_data = self.operScore.find_score_rank(limit)
            
            return jsonify({
                'status': 'success',
                'message': '查询成功',
                'data': rank_data,
                'total': len(rank_data)
            })
            
        except Exception as e:
            self.log.error(f"查询积分排行榜异常: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': f'查询失败: {str(e)}',
                'data': []
            }), 500

class UserScoreController(BaseView):
    """用户积分查询控制器"""
    
    def __init__(self):
        self.operScore = OperScore()
        self.log = DefaultLog().getLogger()
    
    def get(self) -> Dict[str, Any]:
        """
        获取用户积分信息
        :return: 用户积分数据
        """
        try:
            openId = request.args.get('openId')
            if not openId:
                return jsonify({
                    'status': 'error',
                    'message': '缺少openId参数',
                    'data': None
                }), 400
            
            self.log.info(f"查询用户积分: openId={openId}")
            
            user_score = self.operScore.find_score_user(openId)
            
            if user_score:
                return jsonify({
                    'status': 'success',
                    'message': '查询成功',
                    'data': user_score
                })
            else:
                return jsonify({
                    'status': 'error',
                    'message': '用户不存在或查询失败',
                    'data': None
                }), 404
                
        except Exception as e:
            self.log.error(f"查询用户积分异常: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': f'查询失败: {str(e)}',
                'data': None
            }), 500

class SongList(BaseView):
    def get(self):
        try:
            result = SingCore().http_songlist()
            return jsonify(result)
        except Exception as e:
            print("SongList异常------", e)
            return jsonify({"status": "失败", "content": str(e)}) 