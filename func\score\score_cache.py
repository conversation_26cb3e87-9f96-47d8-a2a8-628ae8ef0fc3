"""
积分系统缓存管理器
提供Redis缓存、分布式锁、限流等功能
"""

import json
import time
import threading
from typing import Optional, Dict, Any, List
from contextlib import contextmanager
from func.log.default_log import DefaultLog
from func.score.score_config import ScoreCacheConfig, ScoreSecurityConfig

# 可选导入Redis
try:
    import redis
    from redis.exceptions import RedisError, ConnectionError as RedisConnectionError
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    print("Warning: Redis not available, 将使用内存缓存降级")


class ScoreCacheManager:
    """积分缓存管理器"""
    
    def __init__(self):
        self.log = DefaultLog().getLogger()
        self.redis_client = None
        self.memory_cache = {}  # 降级使用的内存缓存
        self.cache_locks = {}   # 内存缓存锁
        
        # 尝试初始化Redis连接
        if REDIS_AVAILABLE:
            self._init_redis()
        else:
            self.log.warning("Redis不可用，使用内存缓存降级")
    
    def _init_redis(self):
        """初始化Redis连接"""
        try:
            pool = redis.ConnectionPool(
                host=ScoreCacheConfig.REDIS_HOST,
                port=ScoreCacheConfig.REDIS_PORT,
                db=ScoreCacheConfig.REDIS_DB,
                password=ScoreCacheConfig.REDIS_PASSWORD,
                socket_timeout=ScoreCacheConfig.REDIS_SOCKET_TIMEOUT,
                max_connections=ScoreCacheConfig.REDIS_CONNECTION_POOL_SIZE,
                decode_responses=True
            )
            self.redis_client = redis.Redis(connection_pool=pool)
            
            # 测试连接
            self.redis_client.ping()
            self.log.info("Redis缓存初始化成功")
            
        except Exception as e:
            self.log.warning(f"Redis初始化失败，使用内存缓存降级: {e}")
            self.redis_client = None
    
    def get_daily_score(self, openId: str) -> int:
        """获取用户今日积分"""
        today = time.strftime("%Y-%m-%d")
        key = f"{ScoreCacheConfig.DAILY_SCORE_PREFIX}:{openId}:{today}"
        
        try:
            if self.redis_client:
                result = self.redis_client.get(key)
                return int(result) if result else 0
            else:
                return self.memory_cache.get(key, 0)
        except Exception as e:
            self.log.error(f"获取每日积分缓存失败: {e}")
            return 0
    
    def increment_daily_score(self, openId: str, score: int) -> int:
        """增加用户今日积分"""
        today = time.strftime("%Y-%m-%d")
        key = f"{ScoreCacheConfig.DAILY_SCORE_PREFIX}:{openId}:{today}"
        
        try:
            if self.redis_client:
                new_score = self.redis_client.incrby(key, score)
                self.redis_client.expire(key, ScoreCacheConfig.DAILY_SCORE_TTL)
                return new_score
            else:
                # 内存缓存需要加锁
                with self._get_memory_lock(key):
                    current = self.memory_cache.get(key, 0)
                    new_score = current + score
                    self.memory_cache[key] = new_score
                    return new_score
        except Exception as e:
            self.log.error(f"更新每日积分缓存失败: {e}")
            return score
    
    def get_user_score_cache(self, openId: str) -> Optional[Dict[str, Any]]:
        """获取用户积分缓存"""
        key = f"{ScoreCacheConfig.USER_SCORE_PREFIX}:{openId}"
        
        try:
            if self.redis_client:
                result = self.redis_client.get(key)
                return json.loads(result) if result else None
            else:
                return self.memory_cache.get(key)
        except Exception as e:
            self.log.error(f"获取用户积分缓存失败: {e}")
            return None
    
    def set_user_score_cache(self, openId: str, user_data: Dict[str, Any]):
        """设置用户积分缓存"""
        key = f"{ScoreCacheConfig.USER_SCORE_PREFIX}:{openId}"
        
        try:
            if self.redis_client:
                self.redis_client.setex(
                    key, 
                    ScoreCacheConfig.USER_SCORE_TTL, 
                    json.dumps(user_data, ensure_ascii=False)
                )
            else:
                self.memory_cache[key] = user_data
        except Exception as e:
            self.log.error(f"设置用户积分缓存失败: {e}")
    
    def get_rank_cache(self, limit: int) -> Optional[List[Dict[str, Any]]]:
        """获取排行榜缓存"""
        key = f"{ScoreCacheConfig.RANK_CACHE_PREFIX}:{limit}"
        
        try:
            if self.redis_client:
                result = self.redis_client.get(key)
                return json.loads(result) if result else None
            else:
                return self.memory_cache.get(key)
        except Exception as e:
            self.log.error(f"获取排行榜缓存失败: {e}")
            return None
    
    def set_rank_cache(self, limit: int, rank_data: List[Dict[str, Any]]):
        """设置排行榜缓存"""
        key = f"{ScoreCacheConfig.RANK_CACHE_PREFIX}:{limit}"
        
        try:
            if self.redis_client:
                self.redis_client.setex(
                    key, 
                    ScoreCacheConfig.RANK_CACHE_TTL, 
                    json.dumps(rank_data, ensure_ascii=False)
                )
            else:
                self.memory_cache[key] = rank_data
        except Exception as e:
            self.log.error(f"设置排行榜缓存失败: {e}")
    
    def clear_user_cache(self, openId: str):
        """清除用户相关缓存"""
        try:
            if self.redis_client:
                # 清除用户积分缓存
                user_key = f"{ScoreCacheConfig.USER_SCORE_PREFIX}:{openId}"
                self.redis_client.delete(user_key)
                
                # 清除排行榜缓存（因为用户积分可能影响排行榜）
                rank_keys = self.redis_client.keys(f"{ScoreCacheConfig.RANK_CACHE_PREFIX}:*")
                if rank_keys:
                    self.redis_client.delete(*rank_keys)
            else:
                # 清除内存缓存
                keys_to_remove = [k for k in self.memory_cache.keys() 
                                if k.startswith(f"{ScoreCacheConfig.USER_SCORE_PREFIX}:{openId}") 
                                or k.startswith(ScoreCacheConfig.RANK_CACHE_PREFIX)]
                for key in keys_to_remove:
                    del self.memory_cache[key]
        except Exception as e:
            self.log.error(f"清除用户缓存失败: {e}")
    
    @contextmanager
    def distributed_lock(self, resource: str, timeout: int = None):
        """分布式锁上下文管理器"""
        timeout = timeout or ScoreCacheConfig.LOCK_TTL
        lock_key = f"{ScoreCacheConfig.LOCK_PREFIX}:{resource}"
        identifier = f"{time.time()}_{threading.current_thread().ident}"
        
        acquired = False
        try:
            if self.redis_client:
                # Redis分布式锁
                acquired = self.redis_client.set(lock_key, identifier, nx=True, ex=timeout)
                if not acquired:
                    raise Exception(f"获取分布式锁失败: {resource}")
            else:
                # 内存锁降级
                lock = self._get_memory_lock(lock_key)
                acquired = lock.acquire(timeout=timeout)
                if not acquired:
                    raise Exception(f"获取内存锁失败: {resource}")
            
            yield identifier
            
        finally:
            if acquired:
                try:
                    if self.redis_client:
                        # 只有持有锁的进程才能释放
                        lua_script = """
                        if redis.call("get", KEYS[1]) == ARGV[1] then
                            return redis.call("del", KEYS[1])
                        else
                            return 0
                        end
                        """
                        self.redis_client.eval(lua_script, 1, lock_key, identifier)
                    else:
                        lock = self._get_memory_lock(lock_key)
                        lock.release()
                except Exception as e:
                    self.log.error(f"释放锁失败: {e}")
    
    def _get_memory_lock(self, key: str) -> threading.Lock:
        """获取内存锁"""
        if key not in self.cache_locks:
            self.cache_locks[key] = threading.Lock()
        return self.cache_locks[key]
    
    def cleanup_expired_cache(self):
        """清理过期的内存缓存（仅在非Redis模式下使用）"""
        if self.redis_client:
            return  # Redis自动处理过期
        
        try:
            current_time = time.time()
            today = time.strftime("%Y-%m-%d")
            
            # 清理过期的每日积分缓存
            keys_to_remove = []
            for key in self.memory_cache.keys():
                if key.startswith(ScoreCacheConfig.DAILY_SCORE_PREFIX):
                    # 检查日期是否为今天
                    if today not in key:
                        keys_to_remove.append(key)
            
            for key in keys_to_remove:
                del self.memory_cache[key]
            
            if keys_to_remove:
                self.log.info(f"清理了{len(keys_to_remove)}个过期的内存缓存")
                
        except Exception as e:
            self.log.error(f"清理过期缓存失败: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = {
            "redis_available": self.redis_client is not None,
            "memory_cache_size": len(self.memory_cache),
            "cache_locks_count": len(self.cache_locks)
        }
        
        if self.redis_client:
            try:
                info = self.redis_client.info()
                stats.update({
                    "redis_connected_clients": info.get("connected_clients", 0),
                    "redis_used_memory": info.get("used_memory_human", "0B"),
                    "redis_keyspace_hits": info.get("keyspace_hits", 0),
                    "redis_keyspace_misses": info.get("keyspace_misses", 0)
                })
            except Exception as e:
                self.log.error(f"获取Redis统计信息失败: {e}")
        
        return stats
