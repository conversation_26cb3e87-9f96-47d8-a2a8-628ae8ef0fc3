"""
积分系统监控指标管理器
提供业务指标收集、统计分析、告警等功能
"""

import time
import threading
from typing import Dict, Any, List, Optional
from collections import defaultdict, deque
from func.log.default_log import DefaultLog


class ScoreMetrics:
    """积分系统指标收集器"""
    
    def __init__(self):
        self.log = DefaultLog().getLogger()
        self.lock = threading.Lock()
        
        # 基础指标
        self.total_operations = 0
        self.successful_operations = 0
        self.failed_operations = 0
        
        # 操作类型统计
        self.operation_stats = defaultdict(lambda: {
            'count': 0,
            'success_count': 0,
            'fail_count': 0,
            'total_score': 0,
            'avg_response_time': 0.0
        })
        
        # 用户活跃度统计
        self.daily_active_users = set()
        self.hourly_active_users = set()
        
        # 响应时间统计（保留最近1000次操作的响应时间）
        self.response_times = deque(maxlen=1000)
        
        # 错误统计
        self.error_stats = defaultdict(int)
        
        # 积分分布统计
        self.score_distribution = {
            'negative': 0,      # 负积分操作
            'small': 0,         # 1-10分
            'medium': 0,        # 11-50分
            'large': 0,         # 51-100分
            'huge': 0           # 100分以上
        }
        
        # 时间窗口统计（最近24小时，按小时分组）
        self.hourly_stats = defaultdict(lambda: {
            'operations': 0,
            'users': set(),
            'total_score': 0,
            'errors': 0
        })
        
        # 启动时间
        self.start_time = time.time()
    
    def record_operation(self, operation_type: str, success: bool, response_time: float, 
                        user_id: str = None, score: int = 0, error_type: str = None):
        """
        记录操作指标
        :param operation_type: 操作类型
        :param success: 是否成功
        :param response_time: 响应时间（秒）
        :param user_id: 用户ID
        :param score: 积分变化
        :param error_type: 错误类型
        """
        with self.lock:
            current_hour = int(time.time()) // 3600
            
            # 基础指标
            self.total_operations += 1
            if success:
                self.successful_operations += 1
            else:
                self.failed_operations += 1
                if error_type:
                    self.error_stats[error_type] += 1
            
            # 操作类型统计
            op_stats = self.operation_stats[operation_type]
            op_stats['count'] += 1
            if success:
                op_stats['success_count'] += 1
                op_stats['total_score'] += score
            else:
                op_stats['fail_count'] += 1
            
            # 更新平均响应时间
            old_avg = op_stats['avg_response_time']
            op_stats['avg_response_time'] = (old_avg * (op_stats['count'] - 1) + response_time) / op_stats['count']
            
            # 响应时间统计
            self.response_times.append(response_time)
            
            # 用户活跃度
            if user_id:
                self.daily_active_users.add(user_id)
                self.hourly_active_users.add(user_id)
                
                # 时间窗口统计
                hour_stats = self.hourly_stats[current_hour]
                hour_stats['operations'] += 1
                hour_stats['users'].add(user_id)
                if success:
                    hour_stats['total_score'] += score
                else:
                    hour_stats['errors'] += 1
            
            # 积分分布统计
            if success and score != 0:
                if score < 0:
                    self.score_distribution['negative'] += 1
                elif 1 <= score <= 10:
                    self.score_distribution['small'] += 1
                elif 11 <= score <= 50:
                    self.score_distribution['medium'] += 1
                elif 51 <= score <= 100:
                    self.score_distribution['large'] += 1
                else:
                    self.score_distribution['huge'] += 1
    
    def get_basic_stats(self) -> Dict[str, Any]:
        """获取基础统计信息"""
        with self.lock:
            uptime = time.time() - self.start_time
            success_rate = (self.successful_operations / self.total_operations * 100) if self.total_operations > 0 else 0
            
            # 计算平均响应时间
            avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0
            
            # 计算P95响应时间
            sorted_times = sorted(self.response_times)
            p95_response_time = sorted_times[int(len(sorted_times) * 0.95)] if sorted_times else 0
            
            return {
                'uptime_seconds': uptime,
                'total_operations': self.total_operations,
                'successful_operations': self.successful_operations,
                'failed_operations': self.failed_operations,
                'success_rate': round(success_rate, 2),
                'daily_active_users': len(self.daily_active_users),
                'hourly_active_users': len(self.hourly_active_users),
                'avg_response_time': round(avg_response_time, 3),
                'p95_response_time': round(p95_response_time, 3),
                'operations_per_second': round(self.total_operations / uptime, 2) if uptime > 0 else 0
            }
    
    def get_operation_stats(self) -> Dict[str, Any]:
        """获取操作类型统计"""
        with self.lock:
            stats = {}
            for op_type, data in self.operation_stats.items():
                success_rate = (data['success_count'] / data['count'] * 100) if data['count'] > 0 else 0
                avg_score = data['total_score'] / data['success_count'] if data['success_count'] > 0 else 0
                
                stats[op_type] = {
                    'total_count': data['count'],
                    'success_count': data['success_count'],
                    'fail_count': data['fail_count'],
                    'success_rate': round(success_rate, 2),
                    'total_score': data['total_score'],
                    'avg_score': round(avg_score, 2),
                    'avg_response_time': round(data['avg_response_time'], 3)
                }
            
            return stats
    
    def get_error_stats(self) -> Dict[str, Any]:
        """获取错误统计"""
        with self.lock:
            total_errors = sum(self.error_stats.values())
            error_details = {}
            
            for error_type, count in self.error_stats.items():
                percentage = (count / total_errors * 100) if total_errors > 0 else 0
                error_details[error_type] = {
                    'count': count,
                    'percentage': round(percentage, 2)
                }
            
            return {
                'total_errors': total_errors,
                'error_rate': round((total_errors / self.total_operations * 100), 2) if self.total_operations > 0 else 0,
                'error_details': error_details
            }
    
    def get_score_distribution(self) -> Dict[str, Any]:
        """获取积分分布统计"""
        with self.lock:
            total_score_operations = sum(self.score_distribution.values())
            distribution = {}
            
            for category, count in self.score_distribution.items():
                percentage = (count / total_score_operations * 100) if total_score_operations > 0 else 0
                distribution[category] = {
                    'count': count,
                    'percentage': round(percentage, 2)
                }
            
            return {
                'total_score_operations': total_score_operations,
                'distribution': distribution
            }
    
    def get_hourly_trends(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取小时级趋势数据"""
        with self.lock:
            current_hour = int(time.time()) // 3600
            trends = []
            
            for i in range(hours):
                hour = current_hour - i
                stats = self.hourly_stats.get(hour, {
                    'operations': 0,
                    'users': set(),
                    'total_score': 0,
                    'errors': 0
                })
                
                trends.append({
                    'hour': hour,
                    'timestamp': hour * 3600,
                    'operations': stats['operations'],
                    'active_users': len(stats['users']),
                    'total_score': stats['total_score'],
                    'errors': stats['errors'],
                    'error_rate': round((stats['errors'] / stats['operations'] * 100), 2) if stats['operations'] > 0 else 0
                })
            
            return list(reversed(trends))  # 按时间正序返回
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        with self.lock:
            if not self.response_times:
                return {
                    'avg_response_time': 0,
                    'min_response_time': 0,
                    'max_response_time': 0,
                    'p50_response_time': 0,
                    'p95_response_time': 0,
                    'p99_response_time': 0
                }
            
            sorted_times = sorted(self.response_times)
            length = len(sorted_times)
            
            return {
                'avg_response_time': round(sum(sorted_times) / length, 3),
                'min_response_time': round(sorted_times[0], 3),
                'max_response_time': round(sorted_times[-1], 3),
                'p50_response_time': round(sorted_times[int(length * 0.5)], 3),
                'p95_response_time': round(sorted_times[int(length * 0.95)], 3),
                'p99_response_time': round(sorted_times[int(length * 0.99)], 3)
            }
    
    def reset_hourly_users(self):
        """重置小时活跃用户（定时任务调用）"""
        with self.lock:
            self.hourly_active_users.clear()
    
    def reset_daily_users(self):
        """重置日活跃用户（定时任务调用）"""
        with self.lock:
            self.daily_active_users.clear()
    
    def cleanup_old_data(self, hours_to_keep: int = 168):  # 默认保留7天数据
        """清理旧数据"""
        with self.lock:
            current_hour = int(time.time()) // 3600
            cutoff_hour = current_hour - hours_to_keep
            
            # 清理小时统计数据
            keys_to_remove = [hour for hour in self.hourly_stats.keys() if hour < cutoff_hour]
            for key in keys_to_remove:
                del self.hourly_stats[key]
            
            if keys_to_remove:
                self.log.info(f"清理了{len(keys_to_remove)}小时的旧统计数据")
    
    def get_all_metrics(self) -> Dict[str, Any]:
        """获取所有指标"""
        return {
            'basic_stats': self.get_basic_stats(),
            'operation_stats': self.get_operation_stats(),
            'error_stats': self.get_error_stats(),
            'score_distribution': self.get_score_distribution(),
            'performance_metrics': self.get_performance_metrics(),
            'hourly_trends': self.get_hourly_trends(24)
        }


class ScoreAlertManager:
    """积分系统告警管理器"""
    
    def __init__(self, metrics: ScoreMetrics):
        self.log = DefaultLog().getLogger()
        self.metrics = metrics
        self.alert_thresholds = {
            'error_rate': 5.0,          # 错误率超过5%
            'response_time': 2.0,       # 平均响应时间超过2秒
            'operations_per_second': 100, # 每秒操作数超过100
            'failed_operations': 50     # 失败操作数超过50
        }
        self.alert_history = deque(maxlen=100)  # 保留最近100条告警
    
    def check_alerts(self) -> List[Dict[str, Any]]:
        """检查告警条件"""
        alerts = []
        basic_stats = self.metrics.get_basic_stats()
        error_stats = self.metrics.get_error_stats()
        
        # 检查错误率
        if error_stats['error_rate'] > self.alert_thresholds['error_rate']:
            alerts.append({
                'type': 'error_rate',
                'level': 'warning',
                'message': f"错误率过高: {error_stats['error_rate']}%",
                'current_value': error_stats['error_rate'],
                'threshold': self.alert_thresholds['error_rate'],
                'timestamp': time.time()
            })
        
        # 检查响应时间
        if basic_stats['avg_response_time'] > self.alert_thresholds['response_time']:
            alerts.append({
                'type': 'response_time',
                'level': 'warning',
                'message': f"平均响应时间过长: {basic_stats['avg_response_time']}秒",
                'current_value': basic_stats['avg_response_time'],
                'threshold': self.alert_thresholds['response_time'],
                'timestamp': time.time()
            })
        
        # 检查操作频率
        if basic_stats['operations_per_second'] > self.alert_thresholds['operations_per_second']:
            alerts.append({
                'type': 'operations_per_second',
                'level': 'info',
                'message': f"操作频率较高: {basic_stats['operations_per_second']}/秒",
                'current_value': basic_stats['operations_per_second'],
                'threshold': self.alert_thresholds['operations_per_second'],
                'timestamp': time.time()
            })
        
        # 检查失败操作数
        if basic_stats['failed_operations'] > self.alert_thresholds['failed_operations']:
            alerts.append({
                'type': 'failed_operations',
                'level': 'error',
                'message': f"失败操作数过多: {basic_stats['failed_operations']}",
                'current_value': basic_stats['failed_operations'],
                'threshold': self.alert_thresholds['failed_operations'],
                'timestamp': time.time()
            })
        
        # 记录告警历史
        for alert in alerts:
            self.alert_history.append(alert)
            self.log.warning(f"积分系统告警: {alert['message']}")
        
        return alerts
    
    def get_alert_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取告警历史"""
        return list(self.alert_history)[-limit:]
    
    def update_thresholds(self, thresholds: Dict[str, float]):
        """更新告警阈值"""
        self.alert_thresholds.update(thresholds)
        self.log.info(f"告警阈值已更新: {thresholds}")
