import asyncio
import time
import traceback
import random
from typing import List, Optional, Dict, Any, Tuple
from blinker.base import F
from rich.traceback import install

from src.config.config import global_config
from src.common.logger import get_logger
from src.chat.message_receive.chat_stream import Chat<PERSON><PERSON><PERSON>, get_chat_manager
from src.chat.utils.prompt_builder import global_prompt_manager
from src.chat.utils.timer_calculator import Timer
from src.chat.utils.chat_message_builder import get_raw_msg_by_timestamp_with_chat
from src.chat.planner_actions.planner import ActionPlanner
from src.chat.planner_actions.action_modifier import ActionModifier
from src.chat.planner_actions.action_manager import ActionManager
from src.chat.chat_loop.hfc_utils import CycleDetail
from src.person_info.relationship_builder_manager import relationship_builder_manager
from src.person_info.person_info import get_person_info_manager
from src.plugin_system.base.component_types import ActionInfo, ChatMode
from src.plugin_system.apis import generator_api, send_api, message_api, database_api
from src.chat.willing.willing_manager import get_willing_manager
from src.mais4u.mai_think import mai_thinking_manager
from src.mais4u.constant_s4u import ENABLE_S4U
from src.plugins.built_in.core_actions.no_reply import NoReplyAction
from src.chat.chat_loop.hfc_utils import send_typing, stop_typing

from src.chat.message_receive.message import MessageRecv

ERROR_LOOP_INFO = {
    "loop_plan_info": {
        "action_result": {
            "action_type": "error",
            "action_data": {},
            "reasoning": "循环处理失败",
        },
    },
    "loop_action_info": {
        "action_taken": False,
        "reply_text": "",
        "command": "",
        "taken_time": time.time(),
    },
}

NO_ACTION = {
    "action_result": {
        "action_type": "no_action",
        "action_data": {},
        "reasoning": "规划器初始化默认",
        "is_parallel": True,
    },
    "chat_context": "",
    "action_prompt": "",
}

install(extra_lines=3)

# 注释：原来的动作修改超时常量已移除，因为改为顺序执行

logger = get_logger("hfc")  # Logger Name Changed


class HeartFChatting:
    """
    管理一个连续的Focus Chat循环
    用于在特定聊天流中生成回复。
    其生命周期现在由其关联的 SubHeartflow 的 FOCUSED 状态控制。
    """

    def __init__(
        self,
        chat_id: str,
    ):
        """
        HeartFChatting 初始化函数

        参数:
            chat_id: 聊天流唯一标识符(如stream_id)
            on_stop_focus_chat: 当收到stop_focus_chat命令时调用的回调函数
            performance_version: 性能记录版本号，用于区分不同启动版本
        """
        # 基础属性
        self.stream_id: str = chat_id  # 聊天流ID
        self.chat_stream: ChatStream = get_chat_manager().get_stream(self.stream_id)  # type: ignore
        if not self.chat_stream:
            raise ValueError(f"无法找到聊天流: {self.stream_id}")
        self.log_prefix = f"[{get_chat_manager().get_stream_name(self.stream_id) or self.stream_id}]"

        self.relationship_builder = relationship_builder_manager.get_or_create_builder(self.stream_id)

        self.loop_mode = ChatMode.NORMAL  # 初始循环模式为普通模式

        self.action_manager = ActionManager()
        self.action_planner = ActionPlanner(chat_id=self.stream_id, action_manager=self.action_manager)
        self.action_modifier = ActionModifier(action_manager=self.action_manager, chat_id=self.stream_id)

        # 循环控制内部状态
        self._running: bool = False
        self._loop_task: Optional[asyncio.Task] = None  # 主循环任务
        self._energy_task: Optional[asyncio.Task] = None

        # 添加循环信息管理相关的属性
        self.history_loop: List[CycleDetail] = []
        self._cycle_counter = 0
        self._current_cycle_detail: CycleDetail = None  # type: ignore

        self.reply_timeout_count = 0
        self.plan_timeout_count = 0

        self.last_read_time = time.time() - 1

        self.willing_manager = get_willing_manager()

        logger.debug(f"{self.log_prefix} HeartFChatting 初始化完成")

        self.energy_value = 5

    @property
    def running(self) -> bool:
        """获取运行状态"""
        return self._running

    @running.setter
    def running(self, value: bool):
        """设置运行状态，并记录调用栈"""
        if self._running != value:
            import traceback
            stack = traceback.extract_stack()
            caller_info = []
            for frame in stack[-4:-1]:  # 获取最近的3层调用栈（排除当前函数）
                caller_info.append(f"{frame.filename}:{frame.lineno} in {frame.name}")
            caller_chain = " -> ".join(caller_info)

            logger.debug(f"{self.log_prefix} [running] 状态变更: {self._running} -> {value}")
            logger.debug(f"{self.log_prefix} [running] 调用链: {caller_chain}")

            self._running = value

    def _start_task_monitor(self):
        """启动任务监控器"""
        async def monitor_tasks():
            while self.running:
                try:
                    await asyncio.sleep(5)  # 每5秒检查一次

                    # 检查主循环任务状态
                    if self._loop_task:
                        if self._loop_task.cancelled():
                            logger.warning(f"{self.log_prefix} [monitor] 主循环任务被取消")
                            break
                        elif self._loop_task.done() and not self._loop_task.cancelled():
                            if exception := self._loop_task.exception():
                                logger.error(f"{self.log_prefix} [monitor] 主循环任务异常: {exception}")
                            break

                    # 检查能量循环任务状态
                    if self._energy_task:
                        if self._energy_task.cancelled():
                            logger.warning(f"{self.log_prefix} [monitor] 能量循环任务被取消")
                        elif self._energy_task.done() and not self._energy_task.cancelled():
                            if exception := self._energy_task.exception():
                                logger.error(f"{self.log_prefix} [monitor] 能量循环任务异常: {exception}")

                except asyncio.CancelledError:
                    logger.debug(f"{self.log_prefix} [monitor] 任务监控器被取消")
                    break
                except Exception as e:
                    logger.error(f"{self.log_prefix} [monitor] 任务监控器异常: {e}")

        # 创建监控任务
        monitor_task = asyncio.create_task(monitor_tasks())
        monitor_task.add_done_callback(lambda t: logger.debug(f"{self.log_prefix} [monitor] 任务监控器结束"))

    async def start(self):
        """检查是否需要启动主循环，如果未激活则启动。"""
        logger.debug(f"{self.log_prefix} [start] 开始启动HeartFChatting")

        # 如果循环已经激活，直接返回
        if self.running:
            logger.debug(f"{self.log_prefix} HeartFChatting 已激活，无需重复启动")
            return

        try:
            # 标记为活动状态，防止重复启动
            self.running = True
            logger.debug(f"{self.log_prefix} [start] 设置running=True")

            logger.debug(f"{self.log_prefix} [start] 创建能量循环任务")
            self._energy_task = asyncio.create_task(self._energy_loop())
            self._energy_task.add_done_callback(self._handle_energy_completion)
            logger.debug(f"{self.log_prefix} [start] 能量循环任务已创建，任务ID: {id(self._energy_task)}")

            logger.debug(f"{self.log_prefix} [start] 创建主聊天循环任务")
            self._loop_task = asyncio.create_task(self._main_chat_loop())
            self._loop_task.add_done_callback(self._handle_loop_completion)
            logger.debug(f"{self.log_prefix} [start] 主聊天循环任务已创建，任务ID: {id(self._loop_task)}")

            # 启动任务监控器
            self._start_task_monitor()

            logger.debug(f"{self.log_prefix} HeartFChatting 启动完成")

        except Exception as e:
            # 启动失败时重置状态
            logger.error(f"{self.log_prefix} [start] 启动过程中发生异常: {e}")
            self.running = False
            self._loop_task = None
            logger.error(f"{self.log_prefix} HeartFChatting 启动失败: {e}")
            raise

    async def stop(self):
        """优雅地停止HeartFChatting"""
        logger.debug(f"{self.log_prefix} [stop] 开始停止HeartFChatting")

        # 设置运行状态为False
        self.running = False
        logger.debug(f"{self.log_prefix} [stop] 设置running=False")

        # 取消主循环任务
        if self._loop_task and not self._loop_task.done():
            logger.debug(f"{self.log_prefix} [stop] 取消主循环任务")
            self._loop_task.cancel()
            try:
                await self._loop_task
            except asyncio.CancelledError:
                logger.debug(f"{self.log_prefix} [stop] 主循环任务已取消")

        # 取消能量循环任务
        if self._energy_task and not self._energy_task.done():
            logger.debug(f"{self.log_prefix} [stop] 取消能量循环任务")
            self._energy_task.cancel()
            try:
                await self._energy_task
            except asyncio.CancelledError:
                logger.debug(f"{self.log_prefix} [stop] 能量循环任务已取消")

        logger.debug(f"{self.log_prefix} [stop] HeartFChatting停止完成")

    def _handle_loop_completion(self, task: asyncio.Task):
        """当 _hfc_loop 任务完成时执行的回调"""
        logger.debug(f"{self.log_prefix} [_handle_loop_completion] 主循环任务完成回调被调用")
        logger.debug(f"{self.log_prefix} [_handle_loop_completion] 任务ID: {id(task)}")
        logger.debug(f"{self.log_prefix} [_handle_loop_completion] 任务状态: done={task.done()}, cancelled={task.cancelled()}")

        try:
            if exception := task.exception():
                logger.error(f"{self.log_prefix} [_handle_loop_completion] 任务异常完成")
                logger.error(f"{self.log_prefix} [_handle_loop_completion] 异常类型: {type(exception).__name__}")
                logger.error(f"{self.log_prefix} [_handle_loop_completion] 异常信息: {exception}")
                logger.error(f"{self.log_prefix} HeartFChatting: 脱离了聊天(异常): {exception}")
                logger.error(traceback.format_exc())  # Log full traceback for exceptions
            else:
                logger.debug(f"{self.log_prefix} [_handle_loop_completion] 任务正常完成")
                logger.debug(f"{self.log_prefix} HeartFChatting: 脱离了聊天 (外部停止)")
        except asyncio.CancelledError:
            logger.debug(f"{self.log_prefix} [_handle_loop_completion] 任务被取消")
            logger.debug(f"{self.log_prefix} HeartFChatting: 结束了聊天")

    def start_cycle(self):
        # logger = get_logger()
        logger.debug(f"{self.log_prefix} [start_cycle] 开始新的思考周期")

        self._cycle_counter += 1
        logger.debug(f"{self.log_prefix} [start_cycle] 周期计数器: {self._cycle_counter}")

        self._current_cycle_detail = CycleDetail(self._cycle_counter)
        self._current_cycle_detail.thinking_id = f"tid{str(round(time.time(), 2))}"
        logger.debug(f"{self.log_prefix} [start_cycle] 创建周期详情，thinking_id: {self._current_cycle_detail.thinking_id}")

        cycle_timers = {}
        logger.debug(f"{self.log_prefix} [start_cycle] 初始化计时器")

        return cycle_timers, self._current_cycle_detail.thinking_id

    def end_cycle(self, loop_info, cycle_timers):
        # logger = get_logger()
        logger.debug(f"{self.log_prefix} [end_cycle] 结束思考周期")
        logger.debug(f"{self.log_prefix} [end_cycle] loop_info: {loop_info}")
        logger.debug(f"{self.log_prefix} [end_cycle] cycle_timers: {cycle_timers}")

        self._current_cycle_detail.set_loop_info(loop_info)
        logger.debug(f"{self.log_prefix} [end_cycle] 设置循环信息完成")

        self.history_loop.append(self._current_cycle_detail)
        logger.debug(f"{self.log_prefix} [end_cycle] 添加到历史记录，当前历史长度: {len(self.history_loop)}")

        self._current_cycle_detail.timers = cycle_timers
        self._current_cycle_detail.end_time = time.time()
        logger.debug(f"{self.log_prefix} [end_cycle] 设置结束时间: {self._current_cycle_detail.end_time}")

    def _handle_energy_completion(self, task: asyncio.Task):
        if exception := task.exception():
            logger.error(f"{self.log_prefix} HeartFChatting: 能量循环异常: {exception}")
            logger.error(traceback.format_exc())
        else:
            logger.debug(f"{self.log_prefix} HeartFChatting: 能量循环完成")

    async def _energy_loop(self):
        logger.debug(f"{self.log_prefix} [_energy_loop] 能量循环开始")
        energy_loop_count = 0

        try:
            while self.running:
                energy_loop_count += 1
                logger.debug(f"{self.log_prefix} [_energy_loop] 第{energy_loop_count}次能量更新")

                await asyncio.sleep(10)

                if not self.running:
                    logger.debug(f"{self.log_prefix} [_energy_loop] running状态变为False，退出能量循环")
                    break

                if self.loop_mode == ChatMode.NORMAL:
                    old_energy = self.energy_value
                    self.energy_value -= 0.3
                    self.energy_value = max(self.energy_value, 0.3)
                    logger.debug(f"{self.log_prefix} [_energy_loop] NORMAL模式能量更新: {old_energy:.1f} -> {self.energy_value:.1f}")

                if self.loop_mode == ChatMode.FOCUS:
                    old_energy = self.energy_value
                    self.energy_value -= 0.6
                    self.energy_value = max(self.energy_value, 0.3)
                    logger.debug(f"{self.log_prefix} [_energy_loop] FOCUS模式能量更新: {old_energy:.1f} -> {self.energy_value:.1f}")

        except asyncio.CancelledError:
            logger.debug(f"{self.log_prefix} [_energy_loop] 能量循环被取消，执行了{energy_loop_count}次更新")
            raise
        except Exception as e:
            logger.error(f"{self.log_prefix} [_energy_loop] 能量循环异常: {e}")
            raise
        finally:
            logger.debug(f"{self.log_prefix} [_energy_loop] 能量循环结束，总共执行了{energy_loop_count}次更新")

    def print_cycle_info(self, cycle_timers):
        # 记录循环信息和计时器结果
        timer_strings = []
        for name, elapsed in cycle_timers.items():
            formatted_time = f"{elapsed * 1000:.2f}毫秒" if elapsed < 1 else f"{elapsed:.2f}秒"
            timer_strings.append(f"{name}: {formatted_time}")

        logger.debug(
            f"{self.log_prefix} 第{self._current_cycle_detail.cycle_id}次思考,"
            f"耗时: {self._current_cycle_detail.end_time - self._current_cycle_detail.start_time:.1f}秒, "  # type: ignore
            f"选择动作: {self._current_cycle_detail.loop_plan_info.get('action_result', {}).get('action_type', '未知动作')}"
            + (f"\n详情: {'; '.join(timer_strings)}" if timer_strings else "")
        )

    async def _loopbody(self):
        # logger = get_logger()
        logger.debug(f"{self.log_prefix} [_loopbody] 开始执行循环体")
        logger.debug(f"{self.log_prefix} [_loopbody] 当前模式: {self.loop_mode}, 能量值: {self.energy_value:.1f}")

        try:
            if self.loop_mode == ChatMode.FOCUS:
                logger.debug(f"{self.log_prefix} [_loopbody] 进入FOCUS模式处理")
                # 调用观察方法
                logger.debug(f"{self.log_prefix} [_loopbody] 调用_observe()方法")
                try:
                    observe_result = await self._observe()
                    logger.debug(f"{self.log_prefix} [_loopbody] _observe()返回结果: {observe_result}")
                except asyncio.CancelledError:
                    logger.warning(f"{self.log_prefix} [_loopbody] _observe()被取消")
                    # 检查是否是程序关闭导致的取消
                    current_task = asyncio.current_task()
                    if current_task and current_task.cancelled():
                        logger.warning(f"{self.log_prefix} [_loopbody] 当前任务已被取消，可能是程序关闭")
                        raise  # 重新抛出，让主循环处理
                    observe_result = False
                except Exception as e:
                    logger.error(f"{self.log_prefix} [_loopbody] _observe()执行异常: {e}")
                    observe_result = False

                if observe_result:
                    energy_decrease = 1 / global_config.chat.focus_value
                    logger.debug(f"{self.log_prefix} [_loopbody] 观察成功，能量值减少: {energy_decrease:.3f}")
                    self.energy_value -= energy_decrease
                else:
                    energy_decrease = 3 / global_config.chat.focus_value
                    logger.debug(f"{self.log_prefix} [_loopbody] 观察失败，能量值减少: {energy_decrease:.3f}")
                    self.energy_value -= energy_decrease

                logger.debug(f"{self.log_prefix} [_loopbody] 能量值更新后: {self.energy_value:.1f}")

                if self.energy_value <= 1:
                    logger.debug(f"{self.log_prefix} [_loopbody] 能量值过低({self.energy_value:.1f})，切换到NORMAL模式")
                    self.energy_value = 1
                    self.loop_mode = ChatMode.NORMAL
                    return True

                logger.debug(f"{self.log_prefix} [_loopbody] FOCUS模式处理完成，返回True")
                return True

            elif self.loop_mode == ChatMode.NORMAL:
                logger.debug(f"{self.log_prefix} [_loopbody] 进入NORMAL模式处理")
                logger.debug('loop_mode == ChatMode.NORMAL')

                # 获取新消息
                current_time = time.time()
                logger.debug(f"{self.log_prefix} [_loopbody] 调用get_raw_msg_by_timestamp_with_chat")
                logger.debug(f"{self.log_prefix} [_loopbody] 参数: chat_id={self.stream_id}, timestamp_start={self.last_read_time}, timestamp_end={current_time}")

                new_messages_data = get_raw_msg_by_timestamp_with_chat(
                    chat_id=self.stream_id,
                    timestamp_start=self.last_read_time,
                    timestamp_end=current_time,
                    limit=10,
                    limit_mode="earliest",
                    filter_bot=True,
                )

                logger.debug(f"{self.log_prefix} [_loopbody] 获取到新消息数量: {len(new_messages_data)}")
                logger.info('new_messages_data---0----',new_messages_data)

                # 检查是否需要切换到FOCUS模式
                if global_config.chat.focus_value != 0:
                    logger.debug(f"{self.log_prefix} [_loopbody] focus_value不为0，检查是否需要切换模式")

                    focus_threshold = 3 / pow(global_config.chat.focus_value, 0.5)
                    logger.debug(f"{self.log_prefix} [_loopbody] 消息数量阈值: {focus_threshold:.2f}")

                    if len(new_messages_data) > focus_threshold:
                        new_energy = 10 + (len(new_messages_data) / focus_threshold) * 10
                        logger.debug(f"{self.log_prefix} [_loopbody] 消息过多({len(new_messages_data)} > {focus_threshold:.2f})，切换到FOCUS模式")
                        logger.debug(f"{self.log_prefix} [_loopbody] 设置新能量值: {new_energy:.1f}")
                        self.loop_mode = ChatMode.FOCUS
                        self.energy_value = new_energy
                        return True

                    if self.energy_value >= 30:
                        logger.debug(f"{self.log_prefix} [_loopbody] 能量值过高({self.energy_value:.1f} >= 30)，切换到FOCUS模式")
                        self.loop_mode = ChatMode.FOCUS
                        return True

                # 处理新消息
                if new_messages_data:
                    logger.debug(f"{self.log_prefix} [_loopbody] 开始处理新消息")
                    earliest_messages_data = new_messages_data[0]
                    old_last_read_time = self.last_read_time
                    self.last_read_time = earliest_messages_data.get("time")
                    logger.debug(f"{self.log_prefix} [_loopbody] 更新last_read_time: {old_last_read_time} -> {self.last_read_time}")

                    logger.debug(f"{self.log_prefix} [_loopbody] 调用normal_response()方法")
                    if_think = await self.normal_response(earliest_messages_data)
                    logger.debug(f"{self.log_prefix} [_loopbody] normal_response()返回结果: {if_think}")

                    old_energy = self.energy_value
                    if if_think:
                        factor = max(global_config.chat.focus_value, 0.1)
                        self.energy_value *= 1.1 * factor
                        logger.debug(f"{self.log_prefix} [_loopbody] 进行了思考，能量值按倍数增加: {old_energy:.1f} -> {self.energy_value:.1f} (factor: {factor:.2f})")
                    else:
                        energy_increase = 0.1 * global_config.chat.focus_value
                        self.energy_value += energy_increase
                        logger.debug(f"{self.log_prefix} [_loopbody] 没有进行思考，能量值线性增加: {old_energy:.1f} -> {self.energy_value:.1f} (+{energy_increase:.3f})")

                    logger.debug(f"{self.log_prefix} [_loopbody] 消息处理完成，返回True")
                    return True

                # 没有新消息，等待1秒
                # logger.debug(f"{self.log_prefix} [_loopbody] 没有新消息，等待1秒")
                # await asyncio.sleep(1)
                # logger.debug(f"{self.log_prefix} [_loopbody] 等待完成，返回True")
                return True

        except Exception as e:
            logger.error(f"{self.log_prefix} [_loopbody] 循环体执行异常: {e}")
            import traceback
            logger.error(f"{self.log_prefix} [_loopbody] 异常详情: {traceback.format_exc()}")
            # 异常情况下返回True，让循环继续运行，避免因单次异常导致整个循环退出
            # return True
        return False

    async def build_reply_to_str(self, message_data: dict):
        # logger = get_logger()
        logger.debug(f"{self.log_prefix} [build_reply_to_str] 开始构建回复字符串")
        logger.debug(f"{self.log_prefix} [build_reply_to_str] 消息数据: {message_data}")

        logger.debug(f"{self.log_prefix} [build_reply_to_str] 获取person_info_manager")
        person_info_manager = get_person_info_manager()

        chat_platform = message_data.get("chat_info_platform")
        user_id = message_data.get("user_id")
        logger.debug(f"{self.log_prefix} [build_reply_to_str] 平台: {chat_platform}, 用户ID: {user_id}")

        logger.debug(f"{self.log_prefix} [build_reply_to_str] 调用person_info_manager.get_person_id()")
        person_id = person_info_manager.get_person_id(
            chat_platform,  # type: ignore
            user_id,  # type: ignore
        )
        logger.debug(f"{self.log_prefix} [build_reply_to_str] 获得person_id: {person_id}")

        logger.debug(f"{self.log_prefix} [build_reply_to_str] 调用person_info_manager.get_value()获取person_name")
        person_name = await person_info_manager.get_value(person_id, "person_name")
        logger.debug(f"{self.log_prefix} [build_reply_to_str] 获得person_name: {person_name}")

        processed_text = message_data.get('processed_plain_text')
        result = f"{person_name}:{processed_text}"
        logger.debug(f"{self.log_prefix} [build_reply_to_str] 构建结果: {result}")

        return result

    async def _send_and_store_reply(
        self,
        response_set,
        reply_to_str,
        loop_start_time,
        action_message,
        cycle_timers: Dict[str, float],
        thinking_id,
        plan_result,
    ) -> Tuple[Dict[str, Any], str, Dict[str, float]]:
        # logger = get_logger()
        logger.debug(f"{self.log_prefix} [_send_and_store_reply] 开始发送和存储回复")
        logger.debug(f"{self.log_prefix} [_send_and_store_reply] response_set: {response_set}")
        logger.debug(f"{self.log_prefix} [_send_and_store_reply] reply_to_str: {reply_to_str}")
        logger.debug(f"{self.log_prefix} [_send_and_store_reply] thinking_id: {thinking_id}")

        with Timer("回复发送", cycle_timers):
            logger.debug(f"{self.log_prefix} [_send_and_store_reply] 调用_send_response()")
            reply_text = await self._send_response(response_set, reply_to_str, loop_start_time, action_message)
            logger.debug(f"{self.log_prefix} [_send_and_store_reply] _send_response()返回: {reply_text}")

        # 存储reply action信息
        logger.debug(f"{self.log_prefix} [_send_and_store_reply] 开始存储动作信息")
        person_info_manager = get_person_info_manager()

        chat_platform = action_message.get("chat_info_platform", "")
        user_id = action_message.get("user_id", "")
        logger.debug(f"{self.log_prefix} [_send_and_store_reply] 获取用户信息: platform={chat_platform}, user_id={user_id}")

        person_id = person_info_manager.get_person_id(chat_platform, user_id)
        logger.debug(f"{self.log_prefix} [_send_and_store_reply] 获得person_id: {person_id}")

        person_name = await person_info_manager.get_value(person_id, "person_name")
        logger.debug(f"{self.log_prefix} [_send_and_store_reply] 获得person_name: {person_name}")

        action_prompt_display = f"你对{person_name}进行了回复：{reply_text}"
        logger.debug(f"{self.log_prefix} [_send_and_store_reply] action_prompt_display: {action_prompt_display}")

        logger.debug(f"{self.log_prefix} [_send_and_store_reply] 调用database_api.store_action_info()")
        await database_api.store_action_info(
            chat_stream=self.chat_stream,
            action_build_into_prompt=False,
            action_prompt_display=action_prompt_display,
            action_done=True,
            thinking_id=thinking_id,
            action_data={"reply_text": reply_text, "reply_to": reply_to_str},
            action_name="reply",
        )
        logger.debug(f"{self.log_prefix} [_send_and_store_reply] database_api.store_action_info()完成")

        # 构建循环信息
        logger.debug(f"{self.log_prefix} [_send_and_store_reply] 构建循环信息")
        current_time = time.time()
        loop_info: Dict[str, Any] = {
            "loop_plan_info": {
                "action_result": plan_result.get("action_result", {}),
            },
            "loop_action_info": {
                "action_taken": True,
                "reply_text": reply_text,
                "command": "",
                "taken_time": current_time,
            },
        }
        logger.debug(f"{self.log_prefix} [_send_and_store_reply] 构建的loop_info: {loop_info}")

        logger.debug(f"{self.log_prefix} [_send_and_store_reply] 返回结果")
        return loop_info, reply_text, cycle_timers

    async def _observe(self, message_data: Optional[Dict[str, Any]] = None):
        # sourcery skip: hoist-statement-from-if, merge-comparisons, reintroduce-else
        # logger = get_logger()
        logger.debug(f"{self.log_prefix} [_observe] 开始观察方法")
        logger.debug(f"{self.log_prefix} [_observe] 输入消息数据: {message_data}")

        if not message_data:
            message_data = {}
            logger.debug(f"{self.log_prefix} [_observe] 消息数据为空，使用默认空字典")

        action_type = "no_action"
        reply_text = ""  # 初始化reply_text变量，避免UnboundLocalError
        gen_task = None  # 初始化gen_task变量，避免UnboundLocalError
        reply_to_str = ""  # 初始化reply_to_str变量
        logger.debug(f"{self.log_prefix} [_observe] 初始化变量完成")

        # 创建新的循环信息
        logger.debug(f"{self.log_prefix} [_observe] 调用start_cycle()")
        cycle_timers, thinking_id = self.start_cycle()
        logger.debug(f"{self.log_prefix} [_observe] 获得thinking_id: {thinking_id}")

        logger.debug(f"{self.log_prefix} [_observe] 开始第{self._cycle_counter}次思考[模式：{self.loop_mode}]")

        if ENABLE_S4U:
            logger.debug(f"{self.log_prefix} [_observe] S4U已启用，发送typing状态")
            await send_typing()

        logger.debug(f"{self.log_prefix} [_observe] 进入prompt管理器作用域")
        async with global_prompt_manager.async_message_scope(self.chat_stream.context.get_template_name()):
            loop_start_time = time.time()
            logger.debug(f"{self.log_prefix} [_observe] 循环开始时间: {loop_start_time}")

            logger.debug(f"{self.log_prefix} [_observe] 调用relationship_builder.build_relation()")
            logger.debug(f"{self.log_prefix} [_observe] relationship_builder类型: {type(self.relationship_builder).__name__}")
            await self.relationship_builder.build_relation()
            logger.debug(f"{self.log_prefix} [_observe] relationship_builder.build_relation()执行完成")

            available_actions = {}

            # 第一步：动作修改
            logger.debug(f"{self.log_prefix} [_observe] 开始动作修改阶段")
            logger.debug(f"{self.log_prefix} [_observe] action_modifier类型: {type(self.action_modifier).__name__}")
            logger.debug(f"{self.log_prefix} [_observe] action_manager类型: {type(self.action_manager).__name__}")

            with Timer("动作修改", cycle_timers):
                try:
                    logger.debug(f"{self.log_prefix} [_observe] 调用action_modifier.modify_actions()")
                    logger.debug(f"{self.log_prefix} [_observe] 修改前action_manager中的动作数量: {len(self.action_manager.get_using_actions())}")

                    await self.action_modifier.modify_actions()
                    logger.debug(f"{self.log_prefix} [_observe] action_modifier.modify_actions()执行完成")

                    logger.debug(f"{self.log_prefix} [_observe] 调用action_manager.get_using_actions()")
                    available_actions = self.action_manager.get_using_actions()
                    logger.debug(f"{self.log_prefix} [_observe] 修改后可用动作数量: {len(available_actions)}")
                    logger.debug(f"{self.log_prefix} [_observe] 修改后可用动作列表: {list(available_actions.keys())}")

                    # 详细记录每个动作的信息
                    for action_name, action_info in available_actions.items():
                        logger.debug(f"{self.log_prefix} [_observe] 动作 {action_name}: description={action_info.description}, parallel_action={action_info.parallel_action}")

                except Exception as e:
                    logger.error(f"{self.log_prefix} [_observe] 动作修改失败: {e}")
                    import traceback
                    logger.error(f"{self.log_prefix} [_observe] 动作修改异常详情: {traceback.format_exc()}")

            # 检查是否在normal模式下没有可用动作（除了reply相关动作）
            skip_planner = False
            logger.debug(f"{self.log_prefix} [_observe] 检查是否需要跳过规划器")
            if self.loop_mode == ChatMode.NORMAL:
                logger.debug(f"{self.log_prefix} [_observe] 当前为NORMAL模式，检查非回复动作")
                # 过滤掉reply相关的动作，检查是否还有其他动作
                non_reply_actions = {
                    k: v for k, v in available_actions.items() if k not in ["reply", "no_reply", "no_action"]
                }
                logger.debug(f"{self.log_prefix} [_observe] 非回复动作: {list(non_reply_actions.keys())}")

                if not non_reply_actions:
                    skip_planner = True
                    logger.debug(f"{self.log_prefix} [_observe] Normal模式下没有可用动作，直接回复")

                    # 直接设置为reply动作
                    action_type = "reply"
                    reasoning = ""
                    action_data = {"loop_start_time": loop_start_time}
                    is_parallel = False
                    logger.debug(f"{self.log_prefix} [_observe] 设置直接回复: action_type={action_type}")

                    # 构建plan_result用于后续处理
                    plan_result = {
                        "action_result": {
                            "action_type": action_type,
                            "action_data": action_data,
                            "reasoning": reasoning,
                            "timestamp": time.time(),
                            "is_parallel": is_parallel,
                        },
                        "action_prompt": "",
                    }
                    target_message = message_data
                    logger.debug(f"{self.log_prefix} [_observe] 构建plan_result完成")

                # 如果normal模式且不跳过规划器，开始一个回复生成进程，先准备好回复（其实是和planer同时进行的）
                if not skip_planner:
                    logger.debug(f"{self.log_prefix} [_observe] 不跳过规划器，开始预生成回复")
                    logger.debug(f"{self.log_prefix} [_observe] 调用build_reply_to_str()")
                    reply_to_str = await self.build_reply_to_str(message_data)
                    logger.debug(f"{self.log_prefix} [_observe] reply_to_str: {reply_to_str}")

                    logger.debug(f"{self.log_prefix} [_observe] 创建回复生成任务")
                    gen_task = asyncio.create_task(
                        self._generate_response(
                            message_data=message_data,
                            available_actions=available_actions,
                            reply_to=reply_to_str,
                            request_type="chat.replyer.normal",
                        )
                    )
                    logger.debug(f"{self.log_prefix} [_observe] 回复生成任务已创建，任务ID: {id(gen_task)}")

                    # 添加任务完成回调来监控取消
                    def task_done_callback(task):
                        if task.cancelled():
                            logger.warning(f"{self.log_prefix} [_observe] 预生成任务被取消，任务ID: {id(task)}")
                        elif task.exception():
                            logger.error(f"{self.log_prefix} [_observe] 预生成任务异常: {task.exception()}")
                        else:
                            logger.debug(f"{self.log_prefix} [_observe] 预生成任务正常完成")

                    gen_task.add_done_callback(task_done_callback)

            if not skip_planner:
                logger.debug(f"{self.log_prefix} [_observe] 开始规划器阶段")
                logger.debug(f"{self.log_prefix} [_observe] 当前可用动作数量: {len(available_actions)}")
                logger.debug(f"{self.log_prefix} [_observe] 可用动作列表: {list(available_actions.keys())}")

                with Timer("规划器", cycle_timers):
                    logger.debug(f"{self.log_prefix} [_observe] 调用action_planner.plan(), mode={self.loop_mode}")
                    logger.debug(f"{self.log_prefix} [_observe] action_planner类型: {type(self.action_planner).__name__}")
                    logger.debug(f"{self.log_prefix} [_observe] action_planner.chat_id: {self.action_planner.chat_id}")

                    plan_result, target_message = await self.action_planner.plan(mode=self.loop_mode)

                    logger.debug(f"{self.log_prefix} [_observe] 规划器执行完成")
                    logger.debug(f"{self.log_prefix} [_observe] plan_result类型: {type(plan_result)}")
                    logger.debug(f"{self.log_prefix} [_observe] plan_result内容: {plan_result}")
                    logger.debug(f"{self.log_prefix} [_observe] target_message类型: {type(target_message)}")
                    logger.debug(f"{self.log_prefix} [_observe] target_message内容: {target_message}")

                action_result: Dict[str, Any] = plan_result.get("action_result", {})  # type: ignore
                logger.debug(f"{self.log_prefix} [_observe] 提取action_result: {action_result}")

                action_type, action_data, reasoning, is_parallel = (
                    action_result.get("action_type", "error"),
                    action_result.get("action_data", {}),
                    action_result.get("reasoning", "未提供理由"),
                    action_result.get("is_parallel", True),
                )
                logger.debug(f"{self.log_prefix} [_observe] 解析动作结果: action_type={action_type}, is_parallel={is_parallel}")
                logger.debug(f"{self.log_prefix} [_observe] 动作数据: {action_data}")
                logger.debug(f"{self.log_prefix} [_observe] 推理过程: {reasoning}")

                action_data["loop_start_time"] = loop_start_time
                logger.debug(f"{self.log_prefix} [_observe] 添加loop_start_time到action_data: {loop_start_time}")

            logger.debug(f"{self.log_prefix} [_observe] 最终动作类型: {action_type}")
            if action_type == "reply":
                logger.debug(f"{self.log_prefix} [_observe] {global_config.bot.nickname} 决定进行回复")
            elif is_parallel:
                logger.debug(f"{self.log_prefix} [_observe] {global_config.bot.nickname} 决定进行回复, 同时执行{action_type}动作")
            else:
                logger.debug(f"{self.log_prefix} [_observe] 非并行执行，检查预生成任务")
                # 只有在gen_task存在时才进行相关操作
                if gen_task:
                    logger.debug(f"{self.log_prefix} [_observe] 检查预生成任务状态: done={gen_task.done()}")
                    if not gen_task.done():
                        logger.debug(f"{self.log_prefix} [_observe] 取消预生成任务")
                        gen_task.cancel()
                        logger.debug(f"{self.log_prefix} [_observe] 已取消预生成的回复任务")
                        logger.debug(
                            f"{self.log_prefix} [_observe] {global_config.bot.nickname} 原本想要回复，但选择执行{action_type}，不发表回复"
                        )
                    elif generation_result := gen_task.result():
                        content = " ".join([item[1] for item in generation_result if item[0] == "text"])
                        logger.debug(f"{self.log_prefix} [_observe] 预生成的回复任务已完成，内容: {content}")
                        logger.debug(
                            f"{self.log_prefix} [_observe] {global_config.bot.nickname} 原本想要回复：{content}，但选择执行{action_type}，不发表回复"
                        )
                    else:
                        logger.warning(f"{self.log_prefix} [_observe] 预生成的回复任务未生成有效内容")

            action_message = message_data or target_message
            logger.debug(f"{self.log_prefix} [_observe] 动作消息确定: {action_message}")

            if action_type == "reply":
                logger.debug(f"{self.log_prefix} [_observe] 开始处理回复动作")
                # 等待回复生成完毕
                if self.loop_mode == ChatMode.NORMAL:
                    logger.debug(f"{self.log_prefix} [_observe] NORMAL模式回复处理")
                    # 只有在gen_task存在时才等待
                    if not gen_task:
                        logger.debug(f"{self.log_prefix} [_observe] 没有预生成任务，创建新的回复生成任务")
                        logger.debug(f"{self.log_prefix} [_observe] 调用build_reply_to_str()")
                        reply_to_str = await self.build_reply_to_str(message_data)
                        logger.debug(f"{self.log_prefix} [_observe] reply_to_str: {reply_to_str}")

                        logger.debug(f"{self.log_prefix} [_observe] 创建_generate_response任务")
                        gen_task = asyncio.create_task(
                            self._generate_response(
                                message_data=message_data,
                                available_actions=available_actions,
                                reply_to=reply_to_str,
                                request_type="chat.replyer.normal",
                            )
                        )

                    gather_timeout = global_config.chat.thinking_timeout
                    logger.debug(f"{self.log_prefix} [_observe] 等待回复生成，超时时间: {gather_timeout}s")
                    logger.debug(f"{self.log_prefix} [_observe] 等待的任务ID: {id(gen_task)}, 任务状态: done={gen_task.done()}, cancelled={gen_task.cancelled()}")

                    # 检查当前任务状态
                    current_task = asyncio.current_task()
                    if current_task:
                        logger.debug(f"{self.log_prefix} [_observe] 当前任务ID: {id(current_task)}, 状态: done={current_task.done()}, cancelled={current_task.cancelled()}")

                    try:
                        response_set = await asyncio.wait_for(gen_task, timeout=gather_timeout)
                        logger.debug(f"{self.log_prefix} [_observe] 回复生成完成: {response_set}")
                    except asyncio.TimeoutError:
                        logger.warning(f"{self.log_prefix} [_observe] 回复生成超时>{global_config.chat.thinking_timeout}s，已跳过")
                        logger.debug(f"{self.log_prefix} [_observe] 超时时任务状态: done={gen_task.done()}, cancelled={gen_task.cancelled()}")
                        response_set = None
                    except asyncio.CancelledError:
                        logger.warning(f"{self.log_prefix} [_observe] 回复生成任务被取消")
                        logger.debug(f"{self.log_prefix} [_observe] 取消时任务状态: done={gen_task.done()}, cancelled={gen_task.cancelled()}")

                        # 检查是否是当前任务被取消导致的
                        if current_task and current_task.cancelled():
                            logger.warning(f"{self.log_prefix} [_observe] 当前任务也被取消了")

                        response_set = None

                    # 模型炸了或超时，没有回复内容生成
                    if not response_set:
                        logger.warning(f"{self.log_prefix} [_observe] 模型未生成回复内容")
                        return False
                else:
                    logger.debug(f"{self.log_prefix} [_observe] {global_config.bot.nickname} 决定进行回复 (focus模式)")

                    # 构建reply_to字符串
                    logger.debug(f"{self.log_prefix} [_observe] 调用build_reply_to_str()用于focus模式")
                    reply_to_str = await self.build_reply_to_str(action_message)
                    logger.debug(f"{self.log_prefix} [_observe] reply_to_str: {reply_to_str}")

                    # 生成回复
                    logger.debug(f"{self.log_prefix} [_observe] 开始focus模式回复生成")
                    with Timer("回复生成", cycle_timers):
                        response_set = await self._generate_response(
                            message_data=action_message,
                            available_actions=available_actions,
                            reply_to=reply_to_str,
                            request_type="chat.replyer.focus",
                        )
                    logger.debug(f"{self.log_prefix} [_observe] focus模式回复生成完成: {response_set}")

                    if not response_set:
                        logger.warning(f"{self.log_prefix} [_observe] 模型未生成回复内容")
                        return False

                logger.debug(f"{self.log_prefix} [_observe] 调用_send_and_store_reply()")
                loop_info, reply_text, cycle_timers = await self._send_and_store_reply(
                    response_set, reply_to_str, loop_start_time, action_message, cycle_timers, thinking_id, plan_result
                )
                logger.debug(f"{self.log_prefix} [_observe] 回复发送完成，返回True")

                return True

            else:
                # 并行执行：同时进行回复发送和动作执行
                # 先置空防止未定义错误
                background_reply_task = None
                background_action_task = None
                # 如果是并行执行且在normal模式下，需要等待预生成的回复任务完成并发送回复
                if self.loop_mode == ChatMode.NORMAL and is_parallel and gen_task:

                    async def handle_reply_task() -> Tuple[Optional[Dict[str, Any]], str, Dict[str, float]]:
                        # 等待预生成的回复任务完成
                        gather_timeout = global_config.chat.thinking_timeout
                        try:
                            response_set = await asyncio.wait_for(gen_task, timeout=gather_timeout)

                        except asyncio.TimeoutError:
                            logger.warning(
                                f"{self.log_prefix} 并行执行：回复生成超时>{global_config.chat.thinking_timeout}s，已跳过"
                            )
                            return None, "", {}
                        except asyncio.CancelledError:
                            logger.debug(f"{self.log_prefix} 并行执行：回复生成任务已被取消")
                            return None, "", {}

                        if not response_set:
                            logger.warning(f"{self.log_prefix} 模型超时或生成回复内容为空")
                            return None, "", {}

                        reply_to_str = await self.build_reply_to_str(action_message)
                        loop_info, reply_text, cycle_timers_reply = await self._send_and_store_reply(
                            response_set,
                            reply_to_str,
                            loop_start_time,
                            action_message,
                            cycle_timers,
                            thinking_id,
                            plan_result,
                        )
                        return loop_info, reply_text, cycle_timers_reply

                    # 执行回复任务并赋值到变量
                    background_reply_task = asyncio.create_task(handle_reply_task())

                # 动作执行任务
                async def handle_action_task():
                    with Timer("动作执行", cycle_timers):
                        success, reply_text, command = await self._handle_action(
                            action_type, reasoning, action_data, cycle_timers, thinking_id, action_message
                        )
                    return success, reply_text, command

                # 执行动作任务并赋值到变量
                background_action_task = asyncio.create_task(handle_action_task())

                reply_loop_info = None
                reply_text_from_reply = ""
                action_success = False
                action_reply_text = ""
                action_command = ""

                # 并行执行所有任务
                if background_reply_task:
                    results = await asyncio.gather(
                        background_reply_task, background_action_task, return_exceptions=True
                    )
                    # 处理回复任务结果
                    reply_result = results[0]
                    if isinstance(reply_result, BaseException):
                        logger.error(f"{self.log_prefix} 回复任务执行异常: {reply_result}")
                    elif reply_result and reply_result[0] is not None:
                        reply_loop_info, reply_text_from_reply, _ = reply_result

                    # 处理动作任务结果
                    action_task_result = results[1]
                    if isinstance(action_task_result, BaseException):
                        logger.error(f"{self.log_prefix} 动作任务执行异常: {action_task_result}")
                    else:
                        action_success, action_reply_text, action_command = action_task_result
                else:
                    results = await asyncio.gather(background_action_task, return_exceptions=True)
                    # 只有动作任务
                    action_task_result = results[0]
                    if isinstance(action_task_result, BaseException):
                        logger.error(f"{self.log_prefix} 动作任务执行异常: {action_task_result}")
                    else:
                        action_success, action_reply_text, action_command = action_task_result

                # 构建最终的循环信息
                if reply_loop_info:
                    # 如果有回复信息，使用回复的loop_info作为基础
                    loop_info = reply_loop_info
                    # 更新动作执行信息
                    loop_info["loop_action_info"].update(
                        {
                            "action_taken": action_success,
                            "command": action_command,
                            "taken_time": time.time(),
                        }
                    )
                    reply_text = reply_text_from_reply
                else:
                    # 没有回复信息，构建纯动作的loop_info
                    loop_info = {
                        "loop_plan_info": {
                            "action_result": plan_result.get("action_result", {}),
                        },
                        "loop_action_info": {
                            "action_taken": action_success,
                            "reply_text": action_reply_text,
                            "command": action_command,
                            "taken_time": time.time(),
                        },
                    }
                    reply_text = action_reply_text

        if ENABLE_S4U:
            await stop_typing()
            await mai_thinking_manager.get_mai_think(self.stream_id).do_think_after_response(reply_text)

        self.end_cycle(loop_info, cycle_timers)
        self.print_cycle_info(cycle_timers)

        if self.loop_mode == ChatMode.NORMAL:
            logger.debug(f"{self.log_prefix} [_observe] NORMAL模式，调用willing_manager.after_generate_reply_handle()")
            logger.debug(f"{self.log_prefix} [_observe] after_generate_reply_handle参数: message_id={message_data.get('message_id', '')}")
            await self.willing_manager.after_generate_reply_handle(message_data.get("message_id", ""))
            logger.debug(f"{self.log_prefix} [_observe] willing_manager.after_generate_reply_handle()完成")

        # 管理no_reply计数器：当执行了非no_reply动作时，重置计数器
        if action_type != "no_reply" and action_type != "no_action":
            # 导入NoReplyAction并重置计数器
            NoReplyAction.reset_consecutive_count()
            logger.debug(f"{self.log_prefix} 执行了{action_type}动作，重置no_reply计数器")
            return True
        elif action_type == "no_action":
            # 当执行回复动作时，也重置no_reply计数器s
            NoReplyAction.reset_consecutive_count()
            logger.debug(f"{self.log_prefix} 执行了回复动作，重置no_reply计数器")

        return True

    async def _main_chat_loop(self):
        """主循环，持续进行计划并可能回复消息，直到被外部取消。"""
        logger.debug(f"{self.log_prefix} [_main_chat_loop] 主循环开始，running={self.running}")
        loop_count = 0

        try:
            while self.running:  # 主循环
                loop_count += 1
                logger.debug(f"{self.log_prefix} [_main_chat_loop] 第{loop_count}次循环开始，running={self.running}")

                success = await self._loopbody()
                logger.debug(f"{self.log_prefix} [_main_chat_loop] 第{loop_count}次循环_loopbody返回: {success}")

                # await asyncio.sleep(0.1)

                if not success:
                    logger.warning(f"{self.log_prefix} [_main_chat_loop] _loopbody返回False，准备跳出循环")
                    break

                # 检查当前任务是否被取消
                current_task = asyncio.current_task()
                if current_task and current_task.cancelled():
                    logger.warning(f"{self.log_prefix} [_main_chat_loop] 检测到当前任务已被取消")
                    break

                # 检查是否有外部因素修改了running状态
                if not self.running:
                    logger.warning(f"{self.log_prefix} [_main_chat_loop] 检测到running状态被外部修改为False")
                    break

                # 检查主循环任务本身是否被取消
                if self._loop_task and self._loop_task.cancelled():
                    logger.warning(f"{self.log_prefix} [_main_chat_loop] 检测到主循环任务被取消")
                    break

            logger.debug(f"{self.log_prefix} [_main_chat_loop] 主循环正常结束，总共执行了{loop_count}次循环")
            logger.debug(f"{self.log_prefix} 麦麦已强制离开聊天")

        except asyncio.CancelledError as e:
            # 设置了关闭标志位后被取消是正常流程
            logger.debug(f"{self.log_prefix} [_main_chat_loop] 收到CancelledError，执行了{loop_count}次循环")
            logger.debug(f"{self.log_prefix} [_main_chat_loop] CancelledError详情: {e}")

            # 添加调用栈追踪
            import traceback
            logger.debug(f"{self.log_prefix} [_main_chat_loop] 取消时的调用栈: {traceback.format_exc()}")

            logger.debug(f"{self.log_prefix} 麦麦已关闭聊天0")
            return  # 正常取消，直接返回

        except Exception as e:
            logger.error(f"{self.log_prefix} [_main_chat_loop] 收到异常，执行了{loop_count}次循环")
            logger.error(f"{self.log_prefix} [_main_chat_loop] 异常详情: {e}")
            logger.error(f"{self.log_prefix} 麦麦聊天意外错误")
            logger.error(traceback.format_exc())
            return  # 异常情况，直接返回

        # 理论上不能到这里
        logger.error(f"{self.log_prefix} 麦麦聊天意外错误1，结束了聊天循环")

    async def _handle_action(
        self,
        action: str,
        reasoning: str,
        action_data: dict,
        cycle_timers: Dict[str, float],
        thinking_id: str,
        action_message: dict,
    ) -> tuple[bool, str, str]:
        """
        处理规划动作，使用动作工厂创建相应的动作处理器

        参数:
            action: 动作类型
            reasoning: 决策理由
            action_data: 动作数据，包含不同动作需要的参数
            cycle_timers: 计时器字典
            thinking_id: 思考ID

        返回:
            tuple[bool, str, str]: (是否执行了动作, 思考消息ID, 命令)
        """
        try:
            # 使用工厂创建动作处理器实例
            try:
                action_handler = self.action_manager.create_action(
                    action_name=action,
                    action_data=action_data,
                    reasoning=reasoning,
                    cycle_timers=cycle_timers,
                    thinking_id=thinking_id,
                    chat_stream=self.chat_stream,
                    log_prefix=self.log_prefix,
                    action_message=action_message,
                )
            except Exception as e:
                logger.error(f"{self.log_prefix} 创建动作处理器时出错: {e}")
                traceback.print_exc()
                return False, "", ""

            if not action_handler:
                logger.warning(f"{self.log_prefix} 未能创建动作处理器: {action}")
                return False, "", ""

            # 处理动作并获取结果
            result = await action_handler.handle_action()
            success, reply_text = result
            command = ""

            if reply_text == "timeout":
                self.reply_timeout_count += 1
                if self.reply_timeout_count > 5:
                    logger.warning(
                        f"[{self.log_prefix} ] 连续回复超时次数过多，{global_config.chat.thinking_timeout}秒 内大模型没有返回有效内容，请检查你的api是否速度过慢或配置错误。建议不要使用推理模型，推理模型生成速度过慢。或者尝试拉高thinking_timeout参数，这可能导致回复时间过长。"
                    )
                logger.warning(f"{self.log_prefix} 回复生成超时{global_config.chat.thinking_timeout}s，已跳过")
                return False, "", ""

            return success, reply_text, command

        except Exception as e:
            logger.error(f"{self.log_prefix} 处理{action}时出错: {e}")
            traceback.print_exc()
            return False, "", ""

    async def normal_response(self, message_data: dict) -> bool:
        """
        处理接收到的消息。
        在"兴趣"模式下，判断是否回复并生成内容。
        """
        # logger = get_logger()
        logger.debug(f"{self.log_prefix} [normal_response] 开始处理消息")
        logger.debug(f"{self.log_prefix} [normal_response] 消息数据: {message_data}")

        # 检查并修复 message_id 为 None 的情况
        message_id = message_data.get("message_id")
        logger.debug(f"{self.log_prefix} [normal_response] 消息ID: {message_id}")
        if message_id is None:
            logger.warning(f"{self.log_prefix} [normal_response] 消息的 message_id 为 None，跳过处理")
            return False

        logger.debug(f"{self.log_prefix} [normal_response] 创建MessageRecv对象")
        message_data0 = MessageRecv(message_data)

        groupinfo = message_data0.message_info.group_info
        userinfo = message_data0.message_info.user_info

        logger.debug(f"{self.log_prefix} [normal_response] 用户信息: nickname={userinfo.user_nickname}, user_id={userinfo.user_id}")
        if groupinfo:
            logger.debug(f"{self.log_prefix} [normal_response] 群组信息: group_name={groupinfo.group_name}")

        is_master = False
        is_bili = False
        if userinfo.user_nickname == "一滴水" or userinfo.user_nickname == "agi" or userinfo.user_nickname == "keke" or userinfo.user_id == "1003584303"or userinfo.user_id == "dc89b178fba24e20a036878e8cae3c68":
            is_master = True
        if userinfo.user_nickname == "agi" or userinfo.user_nickname == "keke" or userinfo.user_nickname == "一滴水" or userinfo.user_id == "dc89b178fba24e20a036878e8cae3c68":
            is_bili = True

        logger.debug(f"{self.log_prefix} [normal_response] 权限检查: is_master={is_master}, is_bili={is_bili}")

        # interested_rate = (message_data.interest_value or 0.0) * global_config.chat.willing_amplifier
        interest_value = message_data.get("interest_value") or 0.0
        interested_rate = interest_value * global_config.chat.willing_amplifier
        logger.debug(f"{self.log_prefix} [normal_response] 兴趣值计算: interest_value={interest_value}, willing_amplifier={global_config.chat.willing_amplifier}, interested_rate={interested_rate}")

        logger.debug(f"{self.log_prefix} [normal_response] 调用willing_manager.setup()")
        logger.debug(f"{self.log_prefix} [normal_response] willing_manager类型: {type(self.willing_manager).__name__}")
        self.willing_manager.setup(message_data, self.chat_stream)
        logger.debug(f"{self.log_prefix} [normal_response] willing_manager.setup()完成")

        logger.debug(f"{self.log_prefix} [normal_response] 调用willing_manager.get_reply_probability()")
        reply_probability = await self.willing_manager.get_reply_probability(message_data.get("message_id", ""))
        logger.debug(f"{self.log_prefix} [normal_response] willing_manager.get_reply_probability()返回: {reply_probability}")
        logger.debug(f"{self.log_prefix} [normal_response] 当前ongoing_messages数量: {len(self.willing_manager.ongoing_messages)}")

        talk_frequency = -1.00

        if reply_probability < 1 or is_master:  # 简化逻辑，如果未提及 (reply_probability 为 0)，则获取意愿概率
            logger.debug(f"{self.log_prefix} [normal_response] 检查additional_config")
            # additional_config = getattr(message_data.message_info, 'additional_config', {})
            additional_config = message_data.get("additional_config", {})
            logger.debug(f"{self.log_prefix} [normal_response] additional_config: {additional_config}")
            if additional_config and "maimcore_reply_probability_gain" in additional_config:
                gain = additional_config["maimcore_reply_probability_gain"]
                old_probability = reply_probability
                reply_probability += gain
                reply_probability = min(max(reply_probability, 0), 1)  # 确保概率在 0-1 之间
                logger.debug(f"{self.log_prefix} [normal_response] 概率增益: {old_probability} + {gain} = {reply_probability}")

        logger.debug(f"{self.log_prefix} [normal_response] 调用global_config.chat.get_current_talk_frequency()")
        talk_frequency = global_config.chat.get_current_talk_frequency(self.stream_id)
        logger.debug(f"{self.log_prefix} [normal_response] 聊天频率: {talk_frequency}")

        old_reply_probability = reply_probability
        reply_probability = talk_frequency * reply_probability
        logger.debug(f"{self.log_prefix} [normal_response] 最终回复概率: {old_reply_probability} * {talk_frequency} = {reply_probability}")

        # 处理表情包
        if message_data.get("is_emoji") or message_data.get("is_picid"):
            logger.debug(f"{self.log_prefix} [normal_response] 检测到表情包，回复概率设为0")
            reply_probability = 0

        # 打印消息信息
        mes_name = self.chat_stream.group_info.group_name if self.chat_stream.group_info else "私聊"
        logger.debug(f"{self.log_prefix} [normal_response] 聊天名称: {mes_name}")

        # logger.debug(f"[{mes_name}] 当前聊天频率: {talk_frequency:.2f},兴趣值: {interested_rate:.2f},回复概率: {reply_probability * 100:.1f}%")

        if reply_probability > 0.05:
            logger.debug(
                f"[{mes_name}]"
                f"{userinfo.user_nickname}:"
                f"{message_data.processed_plain_text}[兴趣:{interested_rate:.2f}][回复概率:{reply_probability * 100:.1f}%]"
            )

        # 随机数判断是否回复
        random_value = random.random()
        logger.debug(f"{self.log_prefix} [normal_response] 随机数: {random_value}, 回复概率: {reply_probability}")

        if random_value < reply_probability or is_master:
            logger.debug(f"{self.log_prefix} [normal_response] 决定回复消息 ({random_value} < {reply_probability})")
            logger.debug(f"{self.log_prefix} [normal_response] 调用willing_manager.before_generate_reply_handle()")
            logger.debug(f"{self.log_prefix} [normal_response] before_generate_reply_handle参数: message_id={message_data.get('message_id', '')}")
            await self.willing_manager.before_generate_reply_handle(message_data.get("message_id", ""))
            logger.debug(f"{self.log_prefix} [normal_response] willing_manager.before_generate_reply_handle()完成")

            logger.debug(f"{self.log_prefix} [normal_response] 在NORMAL模式下生成回复")
            try:
                # 在NORMAL模式下，直接生成回复，不调用_observe()
                reply_to = message_data.get("reply_to", "")
                available_actions = {}  # NORMAL模式下不使用复杂动作

                logger.debug(f"{self.log_prefix} [normal_response] 调用_generate_response()")
                response_set = await self._generate_response(
                    message_data=message_data,
                    available_actions=available_actions,
                    reply_to=reply_to,
                    request_type="chat.replyer.normal",
                )

                if response_set:
                    logger.debug(f"{self.log_prefix} [normal_response] 回复生成成功，发送回复")
                    await self._send_response(response_set, reply_to, time.time(), message_data)
                    logger.debug(f"{self.log_prefix} [normal_response] 回复发送完成，返回True")
                    return True
                else:
                    logger.warning(f"{self.log_prefix} [normal_response] 回复生成失败，返回True（已处理）")
                    return True

            except asyncio.CancelledError:
                logger.warning(f"{self.log_prefix} [normal_response] 回复生成被取消")
                # 检查是否是程序关闭导致的取消
                current_task = asyncio.current_task()
                if current_task and current_task.cancelled():
                    logger.warning(f"{self.log_prefix} [normal_response] 当前任务已被取消，可能是程序关闭")
                    raise  # 重新抛出，让上层处理
                return False
            except Exception as e:
                logger.error(f"{self.log_prefix} [normal_response] 回复生成异常: {e}")
                return True  # 即使异常也返回True，表示已处理

        logger.debug(f"{self.log_prefix} [normal_response] 决定不回复消息 ({random_value} >= {reply_probability})")
        # 意愿管理器：注销当前message信息 (无论是否回复，只要处理过就删除)
        logger.debug(f"{self.log_prefix} [normal_response] 调用willing_manager.delete()")
        logger.debug(f"{self.log_prefix} [normal_response] delete参数: message_id={message_data.get('message_id', '')}")
        self.willing_manager.delete(message_data.get("message_id", ""))
        logger.debug(f"{self.log_prefix} [normal_response] willing_manager.delete()完成")
        logger.debug(f"{self.log_prefix} [normal_response] 删除后ongoing_messages数量: {len(self.willing_manager.ongoing_messages)}")
        logger.debug(f"{self.log_prefix} [normal_response] 返回False")
        return False

    async def _generate_response(
        self,
        message_data: dict,
        available_actions: Optional[Dict[str, ActionInfo]],
        reply_to: str,
        request_type: str = "chat.replyer.normal",
    ) -> Optional[list]:
        """生成普通回复"""
        # logger = get_logger()
        logger.debug(f"{self.log_prefix} [_generate_response] 开始生成回复")
        logger.debug(f"{self.log_prefix} [_generate_response] 消息数据: {message_data}")
        logger.debug(f"{self.log_prefix} [_generate_response] 可用动作: {list(available_actions.keys()) if available_actions else None}")
        logger.debug(f"{self.log_prefix} [_generate_response] reply_to: {reply_to}")
        logger.debug(f"{self.log_prefix} [_generate_response] request_type: {request_type}")
        logger.debug(f"{self.log_prefix} [_generate_response] enable_tool: {global_config.tool.enable_tool}")

        try:
            # 检查当前任务状态
            current_task = asyncio.current_task()
            if current_task:
                logger.debug(f"{self.log_prefix} [_generate_response] 当前任务ID: {id(current_task)}, 状态: done={current_task.done()}, cancelled={current_task.cancelled()}")

            logger.debug(f"{self.log_prefix} [_generate_response] 调用generator_api.generate_reply()")
            success, reply_set, _ = await generator_api.generate_reply(
                chat_stream=self.chat_stream,
                reply_to=reply_to,
                available_actions=available_actions,
                enable_tool=global_config.tool.enable_tool,
                request_type=request_type,
            )

            logger.debug(f"{self.log_prefix} [_generate_response] generator_api返回: success={success}, reply_set={reply_set}")

            if not success or not reply_set:
                logger.debug(f"{self.log_prefix} [_generate_response] 对 {message_data.get('processed_plain_text', '')} 的回复生成失败")
                return None

            logger.debug(f"{self.log_prefix} [_generate_response] 回复生成成功，返回reply_set")
            return reply_set

        except asyncio.CancelledError:
            logger.warning(f"{self.log_prefix} [_generate_response] 回复生成被取消")
            # 检查当前任务状态
            current_task = asyncio.current_task()
            if current_task:
                logger.debug(f"{self.log_prefix} [_generate_response] 取消时当前任务状态: done={current_task.done()}, cancelled={current_task.cancelled()}")
            raise  # 重新抛出CancelledError
        except Exception as e:
            logger.error(f"{self.log_prefix} [_generate_response] 回复生成出现错误：{str(e)} {traceback.format_exc()}")
            return None

    async def _send_response(self, reply_set, reply_to, thinking_start_time, message_data) -> str:
        # logger = get_logger()
        logger.debug(f"{self.log_prefix} [_send_response] 开始发送回复")
        logger.debug(f"{self.log_prefix} [_send_response] reply_set: {reply_set}")
        logger.debug(f"{self.log_prefix} [_send_response] reply_to: {reply_to}")
        logger.debug(f"{self.log_prefix} [_send_response] thinking_start_time: {thinking_start_time}")

        current_time = time.time()
        logger.debug(f"{self.log_prefix} [_send_response] 当前时间: {current_time}")

        logger.debug(f"{self.log_prefix} [_send_response] 调用message_api.count_new_messages()")
        new_message_count = message_api.count_new_messages(
            chat_id=self.chat_stream.stream_id, start_time=thinking_start_time, end_time=current_time
        )
        logger.debug(f"{self.log_prefix} [_send_response] 新消息数量: {new_message_count}")

        platform = message_data.get("user_platform", "")
        user_id = message_data.get("user_id", "")
        reply_to_platform_id = f"{platform}:{user_id}"
        logger.debug(f"{self.log_prefix} [_send_response] 平台: {platform}, 用户ID: {user_id}")
        logger.debug(f"{self.log_prefix} [_send_response] reply_to_platform_id: {reply_to_platform_id}")

        random_threshold = random.randint(2, 4)
        need_reply = new_message_count >= random_threshold
        logger.debug(f"{self.log_prefix} [_send_response] 随机阈值: {random_threshold}, 是否需要引用回复: {need_reply}")

        if need_reply:
            logger.debug(f"{self.log_prefix} [_send_response] 从思考到回复，共有{new_message_count}条新消息，使用引用回复")
        else:
            logger.debug(f"{self.log_prefix} [_send_response] 从思考到回复，共有{new_message_count}条新消息，不使用引用回复")

        reply_text = ""
        first_replied = False
        logger.debug(f"{self.log_prefix} [_send_response] 开始遍历reply_set，共{len(reply_set)}个片段")

        for i, reply_seg in enumerate(reply_set):
            data = reply_seg[1]
            logger.debug(f"{self.log_prefix} [_send_response] 处理第{i+1}个片段: {data}")

            if not first_replied:
                logger.debug(f"{self.log_prefix} [_send_response] 发送第一个片段")
                if need_reply:
                    logger.debug(f"{self.log_prefix} [_send_response] 使用引用回复发送")
                    await send_api.text_to_stream(
                        text=data,
                        stream_id=self.chat_stream.stream_id,
                        reply_to=reply_to,
                        reply_to_platform_id=reply_to_platform_id,
                        typing=False,
                    )
                else:
                    logger.debug(f"{self.log_prefix} [_send_response] 不使用引用回复发送")
                    await send_api.text_to_stream(
                        text=data,
                        stream_id=self.chat_stream.stream_id,
                        reply_to_platform_id=reply_to_platform_id,
                        typing=False,
                    )
                first_replied = True
                logger.debug(f"{self.log_prefix} [_send_response] 第一个片段发送完成")
            else:
                logger.debug(f"{self.log_prefix} [_send_response] 发送后续片段")
                await send_api.text_to_stream(
                    text=data,
                    stream_id=self.chat_stream.stream_id,
                    reply_to_platform_id=reply_to_platform_id,
                    typing=True,
                )
                logger.debug(f"{self.log_prefix} [_send_response] 后续片段发送完成")
            reply_text += data

        logger.debug(f"{self.log_prefix} [_send_response] 所有片段发送完成，最终回复文本: {reply_text}")
        return reply_text
